// Index-specific JavaScript for PaySoft

document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Mobile Menu Functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            const isOpen = !mobileMenu.classList.contains('translate-x-full');
            
            if (isOpen) {
                mobileMenu.classList.add('translate-x-full');
                mobileMenuBtn.innerHTML = '<i class="bx bx-menu text-2xl"></i>';
            } else {
                mobileMenu.classList.remove('translate-x-full');
                mobileMenuBtn.innerHTML = '<i class="bx bx-x text-2xl"></i>';
            }
        });

        // Close mobile menu when clicking on nav links
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('translate-x-full');
                mobileMenuBtn.innerHTML = '<i class="bx bx-menu text-2xl"></i>';
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                mobileMenu.classList.add('translate-x-full');
                mobileMenuBtn.innerHTML = '<i class="bx bx-menu text-2xl"></i>';
            }
        });
    }

    // Header Scroll Effect
    const header = document.getElementById('header');
    let lastScrollY = window.scrollY;

    function updateHeader() {
        const currentScrollY = window.scrollY;
        
        if (currentScrollY > 100) {
            header.classList.add('bg-white/95', 'shadow-lg');
            header.classList.remove('bg-white/80');
        } else {
            header.classList.add('bg-white/80');
            header.classList.remove('bg-white/95', 'shadow-lg');
        }

        // Hide/show header on scroll
        if (currentScrollY > lastScrollY && currentScrollY > 200) {
            header.style.transform = 'translateY(-100%)';
        } else {
            header.style.transform = 'translateY(0)';
        }

        lastScrollY = currentScrollY;
    }

    window.addEventListener('scroll', updateHeader);

    // Smooth Scrolling for Navigation Links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const headerHeight = header.offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });

                // Update active nav link
                updateActiveNavLink(targetId);
            }
        });
    });

    // Update Active Navigation Link
    function updateActiveNavLink(activeId) {
        const allNavLinks = document.querySelectorAll('.nav-link');
        
        allNavLinks.forEach(link => {
            link.classList.remove('text-primary-600');
            link.classList.add('text-gray-700');
            
            const underline = link.querySelector('span');
            if (underline) {
                underline.style.width = '0';
            }
        });

        const activeLink = document.querySelector(`a[href="${activeId}"]`);
        if (activeLink && activeLink.classList.contains('nav-link')) {
            activeLink.classList.add('text-primary-600');
            activeLink.classList.remove('text-gray-700');
            
            const underline = activeLink.querySelector('span');
            if (underline) {
                underline.style.width = '100%';
            }
        }
    }

    // Intersection Observer for Section Detection
    const sections = document.querySelectorAll('section[id]');
    const observerOptions = {
        rootMargin: '-20% 0px -80% 0px',
        threshold: 0
    };

    const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                updateActiveNavLink(`#${entry.target.id}`);
            }
        });
    }, observerOptions);

    sections.forEach(section => {
        sectionObserver.observe(section);
    });

    // Feature Tabs Functionality
    const featureTabs = document.querySelectorAll('.feature-tab');
    const tabPanes = document.querySelectorAll('.tab-pane');

    featureTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // Remove active class from all tabs
            featureTabs.forEach(t => {
                t.classList.remove('active', 'bg-gradient-to-r', 'from-primary-500', 'to-secondary-500', 'text-white');
                t.classList.add('text-gray-700');
            });

            // Add active class to clicked tab
            this.classList.add('active', 'bg-gradient-to-r', 'from-primary-500', 'to-secondary-500', 'text-white');
            this.classList.remove('text-gray-700');

            // Hide all tab panes
            tabPanes.forEach(pane => {
                pane.classList.add('hidden');
                pane.classList.remove('active');
            });

            // Show target tab pane
            const targetPane = document.getElementById(`tab-${targetTab}`);
            if (targetPane) {
                targetPane.classList.remove('hidden');
                targetPane.classList.add('active');
            }
        });
    });

    // Demo Video Modal
    const demoButtons = document.querySelectorAll('a[href="#demo"]');
    
    demoButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Create modal overlay
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-2xl w-full">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-gray-900">Démonstration PaySoft</h3>
                        <button class="text-gray-500 hover:text-gray-700 text-2xl" id="close-modal">
                            <i class="bx bx-x"></i>
                        </button>
                    </div>
                    <div class="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                        <div class="text-center">
                            <i class="bx bx-play-circle text-6xl text-primary-500 mb-4"></i>
                            <p class="text-gray-600">Vidéo de démonstration à venir</p>
                            <p class="text-sm text-gray-500 mt-2">Cette fonctionnalité sera disponible prochainement</p>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Close modal functionality
            const closeBtn = modal.querySelector('#close-modal');
            closeBtn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
            
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        });
    });

    // Parallax Effect for Hero Background Elements
    const parallaxElements = document.querySelectorAll('.animate-float');
    
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        
        parallaxElements.forEach((element, index) => {
            const speed = (index + 1) * 0.2;
            element.style.transform = `translateY(${rate * speed}px)`;
        });
    });

    // Typing Animation for Hero Title
    function typeWriter(element, text, speed = 100) {
        let i = 0;
        element.innerHTML = '';
        
        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        
        type();
    }

    // Initialize typing animation on hero title
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        const originalText = heroTitle.textContent;
        setTimeout(() => {
            typeWriter(heroTitle, originalText, 50);
        }, 1000);
    }

    // Counter Animation for Stats
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        
        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = Math.floor(start);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
            }
        }
        
        updateCounter();
    }

    // Initialize counter animations when stats come into view
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const statElements = entry.target.querySelectorAll('[data-count]');
                statElements.forEach(stat => {
                    const target = parseInt(stat.dataset.count);
                    animateCounter(stat, target);
                });
                statsObserver.unobserve(entry.target);
            }
        });
    });

    const statsSection = document.querySelector('.hero-stats');
    if (statsSection) {
        statsObserver.observe(statsSection);
    }

    // Smooth reveal animations for sections
    const revealElements = document.querySelectorAll('.reveal-on-scroll');
    
    const revealObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
                revealObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    revealElements.forEach(element => {
        revealObserver.observe(element);
    });

    // Pricing Toggle Functionality
    const pricingToggle = document.getElementById('pricing-toggle');
    const pricingLabels = document.querySelectorAll('.pricing-toggle-label');
    const usagePrices = document.querySelectorAll('.usage-price');
    const proPrices = document.querySelectorAll('.pro-price');

    if (pricingToggle) {
        pricingToggle.addEventListener('change', function() {
            // Toggle labels
            pricingLabels.forEach(label => {
                label.classList.toggle('active');
                if (label.classList.contains('active')) {
                    label.classList.add('text-primary-600', 'font-semibold');
                    label.classList.remove('text-gray-700');
                } else {
                    label.classList.remove('text-primary-600', 'font-semibold');
                    label.classList.add('text-gray-700');
                }
            });

            // Toggle prices
            if (this.checked) {
                usagePrices.forEach(price => price.classList.add('hidden'));
                proPrices.forEach(price => price.classList.remove('hidden'));
            } else {
                usagePrices.forEach(price => price.classList.remove('hidden'));
                proPrices.forEach(price => price.classList.add('hidden'));
            }
        });
    }

    console.log('PaySoft Index Page Initialized Successfully! 🚀');
});
